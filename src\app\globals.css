@import "tailwindcss";

:root {
  --background: #EEE5DA;
  --foreground: #242424;
  --accent: #4ADE80;
}

.dark {
  --background: #0F0F0F;
  --foreground: #E5E5E5;
  --accent: #4ADE80;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
  height: 100%;
}

body {
  overflow-x: hidden;
  margin: 0;
  padding: 0;
  min-height: 100%;
}

/* Prevent scroll issues */
* {
  box-sizing: border-box;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--background);
}

::-webkit-scrollbar-thumb {
  background: var(--accent);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #22c55e;
}

/* Animation classes for GSAP */
.fade-in {
  opacity: 0;
  transform: translateY(50px);
}

.scale-in {
  opacity: 0;
  transform: scale(0.8);
}

.parallax-slow {
  will-change: transform;
}

.parallax-fast {
  will-change: transform;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-accent: var(--accent);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-sans), system-ui, sans-serif;
  line-height: 1.6;
  transition: background-color 0.3s ease, color 0.3s ease;
}

* {
  box-sizing: border-box;
}
