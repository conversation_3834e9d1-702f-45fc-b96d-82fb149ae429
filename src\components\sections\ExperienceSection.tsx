"use client";

import { ExternalLink, Gith<PERSON>, <PERSON>ap, <PERSON><PERSON>, GitPullRequest, Hash, GraduationCap } from "lucide-react";

// Project data with proper structure
const projects = [
  {
    id: 1,
    name: "<PERSON>row<PERSON>-a<PERSON>",
    description: "AI-Powered browser assistant that enhances web browsing with intelligent automation",
    icon: <Zap className="w-6 h-6" />,
    color: "bg-blue-500",
    status: "Active",
    year: "2024",
    tags: ["AI", "Browser Extension", "TypeScript"],
    link: "#",
    github: "#"
  },
  {
    id: 2,
    name: "Designers",
    description: "A comprehensive design system for developers with reusable components",
    icon: <Palette className="w-6 h-6" />,
    color: "bg-yellow-500",
    status: "Completed",
    year: "2024",
    tags: ["Design System", "React", "Storybook"],
    link: "#",
    github: "#"
  },
  {
    id: 3,
    name: "Workflow",
    description: "Ultimate GitHub PR manager for streamlined code review processes",
    icon: <GitPullRequest className="w-6 h-6" />,
    color: "bg-gray-800",
    status: "Active",
    year: "2023",
    tags: ["GitHub", "Automation", "DevOps"],
    link: "#",
    github: "#"
  },
  {
    id: 4,
    name: "HashNode Blog",
    description: "Technical writing and developer content creation platform",
    icon: <Hash className="w-6 h-6" />,
    color: "bg-purple-600",
    status: "Ongoing",
    year: "2023",
    tags: ["Writing", "Community", "Tech"],
    link: "#",
    github: "#"
  }
];

export default function ExperienceSection() {
  return (
    <section id="experience" className="px-6 lg:px-12 mt-24">
      <div className="max-w-7xl mx-auto">
        <div className="bg-deep-charcoal dark:bg-dark-surface rounded-2xl p-8 lg:p-12">
          <div className="mb-12">
            <h2 className="text-3xl lg:text-4xl font-bold text-light-almond dark:text-dark-text mb-4">
              Cool projects I&apos;ve been a part of
            </h2>
            <p className="text-light-almond/70 dark:text-dark-text/70 text-lg">
              A collection of projects that showcase my passion for building innovative solutions
            </p>
          </div>

          <div className="grid gap-6 lg:gap-8">
            {projects.map((project) => (
              <div
                key={project.id}
                className="group relative bg-light-almond/5 dark:bg-dark-text/5 rounded-xl p-6 border border-light-almond/10 dark:border-dark-text/10 hover:border-light-almond/20 dark:hover:border-dark-text/20 transition-all duration-300 hover:shadow-lg hover:shadow-light-almond/5 dark:hover:shadow-dark-text/5"
              >
                <div className="flex items-start justify-between">
                  <div className="flex items-start space-x-4 flex-1">
                    {/* Project Icon */}
                    <div className={`w-14 h-14 ${project.color} rounded-xl flex items-center justify-center text-white shadow-lg group-hover:scale-105 transition-transform duration-300`}>
                      {project.icon}
                    </div>

                    {/* Project Info */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-3 mb-2">
                        <h3 className="text-xl font-semibold text-light-almond dark:text-dark-text group-hover:text-accent-green dark:group-hover:text-accent-green transition-colors">
                          {project.name}
                        </h3>
                        <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                          project.status === 'Active' ? 'bg-green-500/20 text-green-400' :
                          project.status === 'Completed' ? 'bg-blue-500/20 text-blue-400' :
                          'bg-yellow-500/20 text-yellow-400'
                        }`}>
                          {project.status}
                        </span>
                      </div>

                      <p className="text-light-almond/70 dark:text-dark-text/70 mb-4 leading-relaxed">
                        {project.description}
                      </p>

                      {/* Tags */}
                      <div className="flex flex-wrap gap-2 mb-4">
                        {project.tags.map((tag, tagIndex) => (
                          <span
                            key={tagIndex}
                            className="px-3 py-1 text-xs font-medium bg-light-almond/10 dark:bg-dark-text/10 text-light-almond/80 dark:text-dark-text/80 rounded-full"
                          >
                            {tag}
                          </span>
                        ))}
                      </div>
                    </div>
                  </div>

                  {/* Year and Actions */}
                  <div className="flex flex-col items-end space-y-3">
                    <span className="text-sm font-medium text-light-almond/60 dark:text-dark-text/60">
                      {project.year}
                    </span>

                    {/* Action Buttons */}
                    <div className="flex space-x-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                      <button className="p-2 bg-light-almond/10 dark:bg-dark-text/10 rounded-lg hover:bg-light-almond/20 dark:hover:bg-dark-text/20 transition-colors">
                        <ExternalLink className="w-4 h-4 text-light-almond dark:text-dark-text" />
                      </button>
                      <button className="p-2 bg-light-almond/10 dark:bg-dark-text/10 rounded-lg hover:bg-light-almond/20 dark:hover:bg-dark-text/20 transition-colors">
                        <Github className="w-4 h-4 text-light-almond dark:text-dark-text" />
                      </button>
                    </div>
                  </div>
                </div>

                {/* Hover Effect Border */}
                <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-accent-green/0 via-accent-green/5 to-accent-green/0 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
              </div>
            ))}
          </div>

          {/* Education Section */}
          <div className="mt-16 pt-8 border-t border-light-almond/10 dark:border-dark-text/10">
            <div className="mb-8">
              <h2 className="text-2xl lg:text-3xl font-bold text-light-almond dark:text-dark-text mb-4">
                Education
              </h2>
              <p className="text-light-almond/70 dark:text-dark-text/70">
                Academic foundation that shaped my technical journey
              </p>
            </div>

            <div className="bg-light-almond/5 dark:bg-dark-text/5 rounded-xl p-6 border border-light-almond/10 dark:border-dark-text/10">
              <div className="flex items-start space-x-4">
                <div className="w-14 h-14 bg-blue-600 rounded-xl flex items-center justify-center text-white shadow-lg">
                  <GraduationCap className="w-7 h-7" />
                </div>

                <div className="flex-1">
                  <div className="flex items-start justify-between mb-2">
                    <div>
                      <h3 className="text-xl font-semibold text-light-almond dark:text-dark-text mb-1">
                        Mumbai University
                      </h3>
                      <p className="text-light-almond/80 dark:text-dark-text/80 font-medium">
                        Bachelor of Engineering - Information Technology
                      </p>
                    </div>
                    <span className="text-sm font-medium text-light-almond/60 dark:text-dark-text/60 bg-light-almond/10 dark:bg-dark-text/10 px-3 py-1 rounded-full">
                      2021 - 2024
                    </span>
                  </div>

                  <p className="text-light-almond/70 dark:text-dark-text/70 mb-4">
                    Focused on software engineering, data structures, algorithms, and modern web technologies.
                    Developed strong foundation in computer science principles and practical programming skills.
                  </p>

                  <div className="flex flex-wrap gap-2">
                    {['Data Structures', 'Algorithms', 'Web Development', 'Database Systems', 'Software Engineering'].map((subject, index) => (
                      <span
                        key={index}
                        className="px-3 py-1 text-xs font-medium bg-blue-500/20 text-blue-400 rounded-full"
                      >
                        {subject}
                      </span>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
