"use client";

import { ExternalLink, Gith<PERSON>, <PERSON>ap, <PERSON><PERSON>, GitPullRequest, Hash, GraduationCap } from "lucide-react";

// Project data with proper structure
const projects = [
  {
    id: 1,
    name: "<PERSON>row<PERSON>-a<PERSON>",
    description: "AI-Powered browser assistant that enhances web browsing with intelligent automation",
    icon: <Zap className="w-6 h-6" />,
    color: "bg-blue-500",
    status: "Active",
    year: "2024",
    tags: ["AI", "Browser Extension", "TypeScript"],
    link: "#",
    github: "#"
  },
  {
    id: 2,
    name: "Designers",
    description: "A comprehensive design system for developers with reusable components",
    icon: <Palette className="w-6 h-6" />,
    color: "bg-yellow-500",
    status: "Completed",
    year: "2024",
    tags: ["Design System", "React", "Storybook"],
    link: "#",
    github: "#"
  },
  {
    id: 3,
    name: "Workflow",
    description: "Ultimate GitHub PR manager for streamlined code review processes",
    icon: <GitPullRequest className="w-6 h-6" />,
    color: "bg-gray-800",
    status: "Active",
    year: "2023",
    tags: ["GitHub", "Automation", "DevOps"],
    link: "#",
    github: "#"
  },
  {
    id: 4,
    name: "HashNode Blog",
    description: "Technical writing and developer content creation platform",
    icon: <Hash className="w-6 h-6" />,
    color: "bg-purple-600",
    status: "Ongoing",
    year: "2023",
    tags: ["Writing", "Community", "Tech"],
    link: "#",
    github: "#"
  }
];

export default function ExperienceSection() {
  return (
    <section id="experience" className="px-6 lg:px-12 mt-24">
      <div className="max-w-7xl mx-auto">
        <div className="mb-16">
          <h2 className="text-3xl lg:text-4xl font-bold text-deep-charcoal dark:text-dark-text mb-4">
            Cool projects I&apos;ve been a part of
          </h2>
          <p className="text-deep-charcoal/70 dark:text-dark-text/70 text-lg">
            A collection of projects that showcase my passion for building innovative solutions
          </p>
        </div>

        <div className="space-y-8 md:space-y-12">
          {projects.map((project) => (
            <div
              key={project.id}
              className="group relative py-6 md:py-8 border-b border-deep-charcoal/10 dark:border-dark-text/10 last:border-b-0 hover:bg-deep-charcoal/5 dark:hover:bg-dark-text/5 transition-all duration-300 rounded-lg px-2 md:px-4 -mx-2 md:-mx-4"
            >
              <div className="flex flex-col sm:flex-row sm:items-start gap-4 sm:gap-6">
                <div className="flex items-start space-x-4 flex-1">
                  {/* Project Icon */}
                  <div className={`w-12 h-12 ${project.color} rounded-lg flex items-center justify-center text-white group-hover:scale-105 transition-transform duration-300 flex-shrink-0`}>
                    {project.icon}
                  </div>

                  {/* Project Info */}
                  <div className="flex-1 min-w-0">
                    <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-3 mb-3">
                      <h3 className="text-lg sm:text-xl font-semibold text-deep-charcoal dark:text-dark-text group-hover:text-accent-green transition-colors">
                        {project.name}
                      </h3>
                      <span className={`px-2 py-1 text-xs font-medium rounded-full self-start ${
                        project.status === 'Active' ? 'bg-green-500/20 text-green-600 dark:text-green-400' :
                        project.status === 'Completed' ? 'bg-blue-500/20 text-blue-600 dark:text-blue-400' :
                        'bg-yellow-500/20 text-yellow-600 dark:text-yellow-400'
                      }`}>
                        {project.status}
                      </span>
                    </div>

                    <p className="text-sm sm:text-base text-deep-charcoal/70 dark:text-dark-text/70 mb-4 leading-relaxed">
                      {project.description}
                    </p>

                    {/* Tags */}
                    <div className="flex flex-wrap gap-2">
                      {project.tags.map((tag, tagIndex) => (
                        <span
                          key={tagIndex}
                          className="px-2 sm:px-3 py-1 text-xs font-medium bg-deep-charcoal/10 dark:bg-dark-text/10 text-deep-charcoal/80 dark:text-dark-text/80 rounded-full"
                        >
                          {tag}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>

                {/* Year and Actions */}
                <div className="flex flex-row sm:flex-col items-center sm:items-end justify-between sm:justify-start space-x-4 sm:space-x-0 sm:space-y-4 mt-4 sm:mt-0">
                  <span className="text-sm font-medium text-deep-charcoal/60 dark:text-dark-text/60">
                    {project.year}
                  </span>

                  {/* Action Buttons */}
                  <div className="flex space-x-2 opacity-100 sm:opacity-0 sm:group-hover:opacity-100 transition-opacity duration-300">
                    <button className="p-2 bg-deep-charcoal/10 dark:bg-dark-text/10 rounded-lg hover:bg-deep-charcoal/20 dark:hover:bg-dark-text/20 transition-colors">
                      <ExternalLink className="w-4 h-4 text-deep-charcoal dark:text-dark-text"  />
                    </button>
                    <button className="p-2 bg-deep-charcoal/10 dark:bg-dark-text/10 rounded-lg hover:bg-deep-charcoal/20 dark:hover:bg-dark-text/20 transition-colors">
                      <Github className="w-4 h-4 text-deep-charcoal dark:text-dark-text" />
                    </button>
                  </div>
                </div>
              </div>
            </div>
            ))}
          </div>

        {/* Education Section */}
        <div className="mt-16 md:mt-20 pt-8 md:pt-12 border-t border-deep-charcoal/10 dark:border-dark-text/10">
          <div className="mb-8 md:mb-12">
            <h2 className="text-2xl md:text-3xl lg:text-3xl font-bold text-deep-charcoal dark:text-dark-text mb-3 md:mb-4">
              Education
            </h2>
            <p className="text-sm md:text-base text-deep-charcoal/70 dark:text-dark-text/70">
              Academic foundation that shaped my technical journey
            </p>
          </div>

          <div className="py-6 md:py-8 hover:bg-deep-charcoal/5 dark:hover:bg-dark-text/5 transition-all duration-300 rounded-lg px-2 md:px-4 -mx-2 md:-mx-4">
            <div className="flex flex-col sm:flex-row sm:items-start space-y-4 sm:space-y-0 sm:space-x-4 md:space-x-6">
              <div className="w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center text-white flex-shrink-0">
                <GraduationCap className="w-6 h-6" />
              </div>

              <div className="flex-1 min-w-0">
                {/* Header with responsive layout */}
                <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-2 sm:gap-4 mb-3">
                  <div className="flex-1 min-w-0">
                    <h3 className="text-lg sm:text-xl font-semibold text-deep-charcoal dark:text-dark-text mb-1">
                      Mumbai University
                    </h3>
                    <p className="text-sm sm:text-base text-deep-charcoal/80 dark:text-dark-text/80 font-medium">
                      Bachelor of Engineering - Information Technology
                    </p>
                  </div>
                  <span className="text-sm font-medium text-deep-charcoal/60 dark:text-dark-text/60 flex-shrink-0">
                    2021 - 2024
                  </span>
                </div>

                <p className="text-sm sm:text-base text-deep-charcoal/70 dark:text-dark-text/70 mb-4 leading-relaxed">
                  Focused on software engineering, data structures, algorithms, and modern web technologies.
                  Developed strong foundation in computer science principles and practical programming skills.
                </p>

                <div className="flex flex-wrap gap-2">
                  {['Data Structures', 'Algorithms', 'Web Development', 'Database Systems', 'Software Engineering'].map((subject, index) => (
                    <span
                      key={index}
                      className="px-2 sm:px-3 py-1 text-xs font-medium bg-deep-charcoal/10 dark:bg-dark-text/10 text-deep-charcoal/80 dark:text-dark-text/80 rounded-full"
                    >
                      {subject}
                    </span>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
