# 🎵 Spotify Integration Setup

This portfolio includes a cool Spotify integration that allows visitors to listen to your favorite playlist while browsing your site!

## Features

- **Floating Music Button**: A subtle button that appears in the bottom-right corner
- **Hero Section Link**: A music note link in the hero section
- **Spotify Embed**: Optional embedded Spotify player
- **User-Friendly**: Respects user preferences and doesn't auto-play

## How to Set Up Your Playlist

### 1. Get Your Spotify Playlist ID

1. Open Spotify and go to your favorite playlist
2. Click the "..." menu and select "Share" → "Copy link to playlist"
3. The URL will look like: `https://open.spotify.com/playlist/37i9dQZF1DXcBWIGoYBM5M`
4. The playlist ID is the part after `/playlist/`: `37i9dQZF1DXcBWIGoYBM5M`

### 2. Update the Code

Replace the placeholder playlist ID in these files:

**src/app/layout.tsx:**
```tsx
<FloatingMusicButton 
  playlistId="YOUR_PLAYLIST_ID_HERE" 
  playlistName="🎵 Your Playlist Name"
  delay={3000}
/>
```

**src/components/sections/HeroSection.tsx:**
```tsx
<Link
  href="https://open.spotify.com/playlist/YOUR_PLAYLIST_ID_HERE"
  target="_blank"
  rel="noopener noreferrer"
  className="..."
>
```

### 3. Customize the Experience

You can customize various aspects:

- **Delay**: How long to wait before showing the music button (in milliseconds)
- **Playlist Name**: The display name for your playlist
- **Auto-dismiss**: Whether to hide after user interaction

## Components Included

### FloatingMusicButton
- Appears after a delay in the bottom-right corner
- Expands to show playlist info
- Links directly to Spotify
- Remembers if user dismissed it

### SpotifyEmbed
- Full embedded Spotify player
- Can be added to any section
- Responsive design
- Loading states

### SpotifyPlayer
- Modal-style player prompt
- More prominent call-to-action
- Perfect for first-time visitors

## User Experience Notes

- **No Auto-play**: Respects browser policies and user preferences
- **Session Memory**: Remembers if user dismissed the music button
- **External Links**: Opens Spotify in new tab/app
- **Responsive**: Works on all device sizes
- **Accessible**: Proper ARIA labels and keyboard navigation

## Playlist Recommendations

For the best coding experience, consider playlists with:
- Instrumental or low-vocal music
- Consistent tempo and energy
- 2-4 hour duration
- Focus-friendly genres (lo-fi, ambient, classical, electronic)

## Privacy & Performance

- No tracking or analytics
- Minimal JavaScript footprint
- Lazy loading for embeds
- No cookies or local storage (except session dismissal)

Enjoy sharing your musical taste with your portfolio visitors! 🎵
