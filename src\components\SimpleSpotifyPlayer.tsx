"use client";

import { useState, useEffect, useRef } from 'react';
import { Music, ExternalLink, Play, Pause } from 'lucide-react';

interface SimpleSpotifyPlayerProps {
  playlistId?: string;
  playlistName?: string;
  autoShow?: boolean;
  delay?: number;
}

export default function SimpleSpotifyPlayer({
  playlistId = "37i9dQZF1DXcBWIGoYBM5M",
  playlistName = "Arkit's Favorite Playlist",
  autoShow = true,
  delay = 5000
}: SimpleSpotifyPlayerProps) {
  const [isVisible, setIsVisible] = useState(false);
  const [isAnimating, setIsAnimating] = useState(false);
  const [isDismissed, setIsDismissed] = useState(false);
  const [isPlaying, setIsPlaying] = useState(false);
  const [playerVisible, setPlayerVisible] = useState(false);
  const spotifyPlayerRef = useRef<HTMLIFrameElement>(null);

  useEffect(() => {
    // Check if user has already dismissed it in this session
    const dismissed = sessionStorage.getItem('spotify-player-dismissed');
    if (dismissed) {
      setIsDismissed(true);
      return;
    }

    if (autoShow) {
      const timer = setTimeout(() => {
        setIsVisible(true);
      }, delay);
      return () => clearTimeout(timer);
    }
  }, [autoShow, delay]);

  const handlePlaySpotify = () => {
    if (!isPlaying) {
      // Start playing
      setIsAnimating(true);
      setIsPlaying(true);
      setPlayerVisible(true);

      // Load the Spotify embed with autoplay
      if (spotifyPlayerRef.current) {
        spotifyPlayerRef.current.src = `https://open.spotify.com/embed/playlist/${playlistId}?utm_source=generator&theme=0&autoplay=1&show_cover=0&show_artwork=0`;
      }
    } else {
      // Stop playing
      setIsAnimating(false);
      setIsPlaying(false);
      setPlayerVisible(false);
      if (spotifyPlayerRef.current) {
        spotifyPlayerRef.current.src = '';
      }
    }
  };

  const handleDismiss = () => {
    setIsVisible(false);
    setIsDismissed(true);
    sessionStorage.setItem('spotify-player-dismissed', 'true');
  };

  if (isDismissed || !isVisible) {
    return null;
  }

  return (
    <>
      <div className="fixed bottom-6 right-6 z-40 animate-in slide-in-from-right-2 duration-700">
        <div className="bg-gradient-to-r from-green-500 to-green-600 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 ease-out p-3">
        <div className="flex items-center">
          {/* Spotify Icon with Animation */}
          <div className="relative">
            <div className={`w-10 h-10 bg-white/20 rounded-full flex items-center justify-center transition-transform duration-300 ${isAnimating ? 'music-spin' : ''}`}>
              <div className="w-8 h-8 bg-white/30 rounded-full flex items-center justify-center">
                <Music className={`w-4 h-4 text-white ${isAnimating ? 'animate-pulse' : ''}`} />
              </div>
            </div>
            {/* Spotify logo indicator */}
            <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-300 rounded-full flex items-center justify-center">
              <div className="w-1.5 h-1.5 bg-green-600 rounded-full"></div>
            </div>
            {/* Playing indicator when animating */}
            {isAnimating && (
              <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-300 rounded-full animate-ping"></div>
            )}
          </div>

          {/* Controls */}
          <div className="ml-3 flex items-center space-x-2">
            <button
              onClick={handlePlaySpotify}
              className={`w-8 h-8 bg-white/20 hover:bg-white/30 rounded-full flex items-center justify-center transition-all duration-300 ${isAnimating ? 'pulse-glow' : ''}`}
              title={isPlaying ? "Stop Music" : "Play Music"}
            >
              {isPlaying ? (
                <Pause className="w-3 h-3" />
              ) : (
                <Play className="w-3 h-3 ml-0.5" />
              )}
            </button>
            
            <button
              onClick={() => window.open(`https://open.spotify.com/playlist/${playlistId}`, '_blank')}
              className="w-8 h-8 bg-white/20 hover:bg-white/30 rounded-full flex items-center justify-center transition-colors"
              title="Open Spotify"
            >
              <ExternalLink className="w-3 h-3" />
            </button>
            
            <button
              onClick={handleDismiss}
              className="w-8 h-8 bg-white/20 hover:bg-white/30 rounded-full flex items-center justify-center transition-colors text-xs"
              title="Dismiss"
            >
              ✕
            </button>
          </div>
        </div>

        {/* Track Info */}
        <div className="mt-2 text-center">
          <p className="text-xs text-green-100 truncate max-w-[200px] mx-auto">
            🎵 {playlistName}
          </p>
          <p className="text-xs text-green-200/80">
            Click to open in Spotify
          </p>
        </div>

          {/* Animated Sound Waves when playing */}
          {isAnimating && (
            <div className="absolute -bottom-2 left-1/2 transform -translate-x-1/2 flex space-x-1">
              <div className="w-1 h-3 bg-green-300/80 rounded-full music-wave" style={{ animationDelay: '0s' }}></div>
              <div className="w-1 h-4 bg-green-300/80 rounded-full music-wave" style={{ animationDelay: '0.1s' }}></div>
              <div className="w-1 h-2 bg-green-300/80 rounded-full music-wave" style={{ animationDelay: '0.2s' }}></div>
              <div className="w-1 h-5 bg-green-300/80 rounded-full music-wave" style={{ animationDelay: '0.3s' }}></div>
              <div className="w-1 h-3 bg-green-300/80 rounded-full music-wave" style={{ animationDelay: '0.4s' }}></div>
            </div>
          )}
        </div>
      </div>

      {/* Embedded Spotify Player */}
      {playerVisible && (
        <div className="fixed bottom-20 right-6 z-50 animate-in fade-in duration-300">
          <div className="relative">
            <iframe
              ref={spotifyPlayerRef}
              src=""
              width="300"
              height="152"
              style={{ border: 'none' }}
              allow="autoplay; clipboard-write; encrypted-media; fullscreen; picture-in-picture"
              loading="lazy"
              className="rounded-xl shadow-2xl"
            />
            <button
              onClick={() => {
                setPlayerVisible(false);
                setIsPlaying(false);
                setIsAnimating(false);
                if (spotifyPlayerRef.current) {
                  spotifyPlayerRef.current.src = '';
                }
              }}
              className="absolute -top-2 -right-2 w-6 h-6 bg-gray-800 hover:bg-gray-700 text-white rounded-full flex items-center justify-center text-xs transition-colors"
            >
              ✕
            </button>
          </div>
        </div>
      )}
    </>
  );
}
