"use client";

import { useState, useEffect, useRef } from 'react';
import { Play, Pause, SkipForward, SkipBack, Volume2, VolumeX, Music } from 'lucide-react';

// Define Spotify SDK types
declare global {
  interface Window {
    Spotify: any;
    onSpotifyWebPlaybackSDKReady: () => void;
  }
}

interface SpotifyWebPlaybackProps {
  clientId: string;
  playlistId?: string;
  autoShow?: boolean;
}

export default function SpotifyWebPlayback({
  clientId,
  playlistId = "37i9dQZF1DXcBWIGoYBM5M", // Default: Today's Top Hits
  autoShow = true
}: SpotifyWebPlaybackProps) {
  // Player state
  const [isReady, setIsReady] = useState(false);
  const [player, setPlayer] = useState<any>(null);
  const [deviceId, setDeviceId] = useState<string>("");
  const [isPlaying, setIsPlaying] = useState(false);
  const [isMuted, setIsMuted] = useState(false);
  const [currentTrack, setCurrentTrack] = useState<any>(null);
  const [accessToken, setAccessToken] = useState<string>("");
  const [isVisible, setIsVisible] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const previousVolume = useRef<number>(0.5);

  // Load Spotify SDK
  useEffect(() => {
    if (autoShow) {
      const timer = setTimeout(() => {
        setIsVisible(true);
      }, 4000);
      return () => clearTimeout(timer);
    }
  }, [autoShow]);

  // Load Spotify SDK
  useEffect(() => {
    if (!isVisible) return;

    const script = document.createElement("script");
    script.src = "https://sdk.scdn.co/spotify-player.js";
    script.async = true;
    document.body.appendChild(script);

    window.onSpotifyWebPlaybackSDKReady = () => {
      setIsReady(true);
    };

    return () => {
      document.body.removeChild(script);
    };
  }, [isVisible]);

  // Initialize player when SDK is ready
  useEffect(() => {
    if (!isReady || !accessToken) return;

    const spotifyPlayer = new window.Spotify.Player({
      name: "Arkit's Portfolio Player",
      getOAuthToken: (cb: (token: string) => void) => {
        cb(accessToken);
      },
      volume: 0.5
    });

    // Error handling
    spotifyPlayer.addListener('initialization_error', ({ message }: any) => {
      console.error('Failed to initialize:', message);
      setError('Failed to initialize Spotify player');
    });

    spotifyPlayer.addListener('authentication_error', ({ message }: any) => {
      console.error('Failed to authenticate:', message);
      setError('Authentication failed. Please try again.');
    });

    spotifyPlayer.addListener('account_error', ({ message }: any) => {
      console.error('Failed to validate account:', message);
      setError('Premium account required for playback');
    });

    // Playback status updates
    spotifyPlayer.addListener('player_state_changed', (state: any) => {
      if (!state) return;
      
      setCurrentTrack(state.track_window.current_track);
      setIsPlaying(!state.paused);
    });

    // Ready
    spotifyPlayer.addListener('ready', ({ device_id }: any) => {
      console.log('Ready with Device ID', device_id);
      setDeviceId(device_id);
      
      // Start playing the playlist
      if (playlistId) {
        fetch(`https://api.spotify.com/v1/me/player/play?device_id=${device_id}`, {
          method: 'PUT',
          body: JSON.stringify({
            context_uri: `spotify:playlist:${playlistId}`,
            offset: { position: 0 },
            position_ms: 0
          }),
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${accessToken}`
          }
        }).catch(err => {
          console.error('Error starting playback:', err);
          setError('Error starting playback');
        });
      }
    });

    // Connect to the player
    spotifyPlayer.connect();
    setPlayer(spotifyPlayer);

    return () => {
      spotifyPlayer.disconnect();
    };
  }, [isReady, accessToken, playlistId]);

  // Handle login - simplified approach
  const handleLogin = () => {
    // For now, just open Spotify directly
    window.open('https://open.spotify.com/playlist/37i9dQZF1DXcBWIGoYBM5M', '_blank');

    // Show a message that this requires Premium
    setError('Spotify Web Playback requires Premium. Opening Spotify app instead.');

    // Hide after 3 seconds
    setTimeout(() => {
      setIsVisible(false);
    }, 3000);
  };

  // Extract token from URL after Spotify redirect
  useEffect(() => {
    const hash = window.location.hash;
    if (hash) {
      const tokenMatch = hash.match(/access_token=([^&]*)/);
      if (tokenMatch) {
        const token = tokenMatch[1];
        setAccessToken(token);
        window.location.hash = ''; // Clean URL
      }
    }
  }, []);

  // Player controls
  const handlePlayPause = () => {
    if (player) {
      player.togglePlay();
    }
  };

  const handleNext = () => {
    if (player) {
      player.nextTrack();
    }
  };

  const handlePrevious = () => {
    if (player) {
      player.previousTrack();
    }
  };

  const handleMute = () => {
    if (player) {
      if (isMuted) {
        player.setVolume(previousVolume.current);
      } else {
        previousVolume.current = player._options.volume;
        player.setVolume(0);
      }
      setIsMuted(!isMuted);
    }
  };

  const handleDismiss = () => {
    setIsVisible(false);
  };

  if (!isVisible) {
    return null;
  }

  // Login screen
  if (!accessToken) {
    return (
      <div className="fixed bottom-6 right-6 z-40 animate-in slide-in-from-right-2 duration-700">
        <div className="bg-gradient-to-r from-green-500 to-green-600 text-white rounded-full shadow-lg p-3">
          <div className="flex items-center">
            <div className="relative">
              <div className="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center">
                <Music className="w-4 h-4 text-white" />
              </div>
            </div>
            <div className="ml-3">
              <button
                onClick={handleLogin}
                className="px-3 py-1 bg-white/20 hover:bg-white/30 rounded-full text-sm font-medium transition-colors"
              >
                Connect Spotify
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="fixed bottom-6 right-6 z-40 animate-in slide-in-from-right-2 duration-700">
        <div className="bg-red-500 text-white rounded-full shadow-lg p-3">
          <div className="flex items-center">
            <div className="ml-3">
              <p className="text-sm">{error}</p>
              <button
                onClick={handleDismiss}
                className="text-xs text-white/80 hover:text-white"
              >
                Dismiss
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Loading state
  if (!currentTrack) {
    return (
      <div className="fixed bottom-6 right-6 z-40 animate-in slide-in-from-right-2 duration-700">
        <div className="bg-gradient-to-r from-green-500 to-green-600 text-white rounded-full shadow-lg p-3">
          <div className="flex items-center">
            <div className="relative">
              <div className="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center">
                <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
              </div>
            </div>
            <div className="ml-3">
              <p className="text-sm">Connecting to Spotify...</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Player UI
  return (
    <div className="fixed bottom-6 right-6 z-40 animate-in slide-in-from-right-2 duration-700">
      <div className="bg-gradient-to-r from-green-500 to-green-600 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 ease-out p-3">
        <div className="flex items-center">
          {/* Album Art & Rotating Disc */}
          <div className="relative">
            <div className={`w-10 h-10 bg-white/20 rounded-full flex items-center justify-center transition-transform duration-300 ${isPlaying ? 'music-spin' : ''}`}>
              {currentTrack?.album?.images?.[0]?.url ? (
                <img 
                  src={currentTrack.album.images[0].url} 
                  alt={currentTrack.album.name}
                  className="w-8 h-8 rounded-full object-cover"
                />
              ) : (
                <div className="w-8 h-8 bg-white/30 rounded-full flex items-center justify-center">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                </div>
              )}
            </div>
            {/* Playing indicator */}
            {isPlaying && (
              <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-300 rounded-full animate-ping"></div>
            )}
          </div>

          {/* Controls */}
          <div className="ml-3 flex items-center space-x-2">
            <button
              onClick={handlePrevious}
              className="w-7 h-7 bg-white/20 hover:bg-white/30 rounded-full flex items-center justify-center transition-colors"
            >
              <SkipBack className="w-3 h-3" />
            </button>
            
            <button
              onClick={handlePlayPause}
              className="w-8 h-8 bg-white/20 hover:bg-white/30 rounded-full flex items-center justify-center transition-colors"
            >
              {isPlaying ? <Pause className="w-3 h-3" /> : <Play className="w-3 h-3 ml-0.5" />}
            </button>
            
            <button
              onClick={handleNext}
              className="w-7 h-7 bg-white/20 hover:bg-white/30 rounded-full flex items-center justify-center transition-colors"
            >
              <SkipForward className="w-3 h-3" />
            </button>
            
            <button
              onClick={handleMute}
              className="w-7 h-7 bg-white/20 hover:bg-white/30 rounded-full flex items-center justify-center transition-colors"
            >
              {isMuted ? <VolumeX className="w-3 h-3" /> : <Volume2 className="w-3 h-3" />}
            </button>
            
            <button
              onClick={handleDismiss}
              className="w-7 h-7 bg-white/20 hover:bg-white/30 rounded-full flex items-center justify-center transition-colors text-xs"
            >
              ✕
            </button>
          </div>
        </div>
        
        {/* Track Info */}
        <div className="mt-2 text-center">
          <p className="text-xs text-white truncate max-w-[200px] mx-auto">
            {currentTrack.name} - {currentTrack.artists.map((a: any) => a.name).join(', ')}
          </p>
        </div>
      </div>
    </div>
  );
}
