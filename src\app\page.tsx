"use client";

import { useState } from "react";
import { ThemeToggle } from "@/components/theme-toggle";
import { Footer } from "@/components/footer";
import Image from "next/image";
import Link from "next/link";
import { Menu, X } from "lucide-react";
import { SmoothScrollProvider } from "@/components/animations";
import {
  HeroSection,
  AboutSection,
  ExperienceSection,
  HireMeSection,
  ContributionSection
} from "@/components/sections";



export default function Home() {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  const scrollToSection = (sectionId: string) => {
    document.getElementById(sectionId)?.scrollIntoView({ behavior: 'smooth' });
    setMobileMenuOpen(false);
  };

  return (
    <SmoothScrollProvider>
      <div className="min-h-screen bg-light-almond dark:bg-dark-bg transition-colors">
      {/* Navigation */}
      <nav className="flex items-center justify-between px-6 py-6 lg:px-12 max-w-7xl mx-auto relative">
        {/* Logo */}
        <Link href="/" className="flex items-center space-x-3 text-deep-charcoal dark:text-dark-text hover:opacity-80 transition-opacity">
          <div className="w-10 h-10 relative">
            <Image
              src="/arkit-logo.png"
              alt="Arkit_k Logo"
              fill
              className="object-contain rounded-full"
            />
          </div>
          <span className="text-xl font-bold">Arkit_k</span>
        </Link>

        {/* Desktop Navigation Links */}
        <div className="hidden md:flex items-center space-x-8 text-deep-charcoal dark:text-dark-text">
          <button
            onClick={() => scrollToSection('about')}
            className="hover:text-accent-green transition-colors"
          >
            About
          </button>
          <button
            onClick={() => scrollToSection('experience')}
            className="hover:text-accent-green transition-colors"
          >
            Project
          </button>
          <button
            onClick={() => scrollToSection('hire')}
            className="hover:text-accent-green transition-colors"
          >
            Hire Me
          </button>
          <button
            onClick={() => scrollToSection('contributions')}
            className="hover:text-accent-green transition-colors"
          >
            Contributions
          </button>
        </div>

        {/* Mobile Menu Button & Theme Toggle */}
        <div className="flex items-center gap-4">
          <ThemeToggle />
          <button
            onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
            className="md:hidden p-2 text-deep-charcoal dark:text-dark-text hover:text-accent-green transition-colors"
          >
            {mobileMenuOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
          </button>
        </div>

        {/* Mobile Navigation Menu */}
        {mobileMenuOpen && (
          <div className="absolute top-full left-0 right-0 bg-light-almond dark:bg-dark-bg border-t border-deep-charcoal/10 dark:border-dark-text/10 md:hidden z-50">
            <div className="px-6 py-4 space-y-4">
              <button
                onClick={() => scrollToSection('about')}
                className="block w-full text-left text-deep-charcoal dark:text-dark-text hover:text-accent-green transition-colors py-2"
              >
                About
              </button>
              <button
                onClick={() => scrollToSection('experience')}
                className="block w-full text-left text-deep-charcoal dark:text-dark-text hover:text-accent-green transition-colors py-2"
              >
                Project
              </button>
              <button
                onClick={() => scrollToSection('hire')}
                className="block w-full text-left text-deep-charcoal dark:text-dark-text hover:text-accent-green transition-colors py-2"
              >
                Hire Me
              </button>
              <button
                onClick={() => scrollToSection('contributions')}
                className="block w-full text-left text-deep-charcoal dark:text-dark-text hover:text-accent-green transition-colors py-2"
              >
                Contributions
              </button>
            </div>
          </div>
        )}
      </nav>



      {/* Main Content */}
      <main className="overflow-x-hidden">
        <HeroSection />
        <AboutSection />
        <ExperienceSection />
        <HireMeSection />
        <ContributionSection />
      </main>

      {/* Footer */}
      <Footer />
      </div>
    </SmoothScrollProvider>
  );
}
