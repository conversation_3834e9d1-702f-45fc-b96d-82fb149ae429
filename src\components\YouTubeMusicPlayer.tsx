"use client";

import { useState, useRef, useEffect } from 'react';
import { Play, Pause, Volume2, VolumeX, Music } from 'lucide-react';

interface YouTubeMusicPlayerProps {
  videoId: string; // YouTube video ID for your playlist/music
  title?: string;
  autoShow?: boolean;
}

export default function YouTubeMusicPlayer({ 
  videoId = "jfKfPfyJRdk", // Default: lofi hip hop radio
  title = "Coding Vibes Playlist",
  autoShow = true 
}: YouTubeMusicPlayerProps) {
  const [isVisible, setIsVisible] = useState(false);
  const [isPlaying, setIsPlaying] = useState(false);
  const [isMuted, setIsMuted] = useState(false);
  const [hasUserInteracted, setHasUserInteracted] = useState(false);
  const playerRef = useRef<HTMLIFrameElement>(null);

  useEffect(() => {
    if (autoShow) {
      const timer = setTimeout(() => {
        setIsVisible(true);
      }, 4000);
      return () => clearTimeout(timer);
    }
  }, [autoShow]);

  const handlePlay = () => {
    setHasUserInteracted(true);
    if (playerRef.current) {
      const iframe = playerRef.current;
      if (isPlaying) {
        // Pause by reloading without autoplay
        iframe.src = `https://www.youtube.com/embed/${videoId}?enablejsapi=1&controls=0&modestbranding=1&rel=0&showinfo=0`;
        setIsPlaying(false);
      } else {
        // Play with autoplay
        iframe.src = `https://www.youtube.com/embed/${videoId}?enablejsapi=1&autoplay=1&controls=0&modestbranding=1&rel=0&showinfo=0&mute=${isMuted ? 1 : 0}`;
        setIsPlaying(true);
      }
    }
  };

  const handleMute = () => {
    setIsMuted(!isMuted);
    if (playerRef.current && isPlaying) {
      const iframe = playerRef.current;
      iframe.src = `https://www.youtube.com/embed/${videoId}?enablejsapi=1&autoplay=1&controls=0&modestbranding=1&rel=0&showinfo=0&mute=${!isMuted ? 1 : 0}`;
    }
  };

  const handleDismiss = () => {
    setIsVisible(false);
    if (isPlaying && playerRef.current) {
      playerRef.current.src = '';
      setIsPlaying(false);
    }
  };

  if (!isVisible) {
    return null;
  }

  return (
    <div className="fixed bottom-4 left-4 md:bottom-6 md:left-6 z-40 animate-in slide-in-from-left-2 duration-700">
      <div className="music-player-container bg-gradient-to-r from-red-500 to-red-600 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 ease-out">
        <div className="flex items-center p-2">
          {/* Rotating Music Disc */}
          <div className="relative">
            <div className={`w-10 h-10 bg-white/20 rounded-full flex items-center justify-center transition-all duration-500 ${isPlaying ? 'music-spin' : ''}`}>
              <div className="w-6 h-6 bg-white/30 rounded-full flex items-center justify-center">
                <div className="w-2 h-2 bg-red-500 rounded-full"></div>
              </div>
            </div>
            {/* Vinyl record lines */}
            <div className="absolute inset-1 border border-white/20 rounded-full"></div>
            <div className="absolute inset-2 border border-white/10 rounded-full"></div>

            {/* Playing indicator */}
            {isPlaying && (
              <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-400 rounded-full animate-ping"></div>
            )}
          </div>

          {/* Compact Controls */}
          <div className="ml-2 flex items-center space-x-1">
            <button
              onClick={handlePlay}
              className={`w-7 h-7 bg-white/20 hover:bg-white/30 rounded-full flex items-center justify-center transition-all duration-200 ${isPlaying ? 'music-pulse' : ''}`}
              title={isPlaying ? 'Pause' : 'Play'}
            >
              {isPlaying ? <Pause className="w-3 h-3" /> : <Play className="w-3 h-3 ml-0.5" />}
            </button>

            <button
              onClick={handleMute}
              className="w-7 h-7 bg-white/20 hover:bg-white/30 rounded-full flex items-center justify-center transition-colors"
              title={isMuted ? 'Unmute' : 'Mute'}
            >
              {isMuted ? <VolumeX className="w-3 h-3" /> : <Volume2 className="w-3 h-3" />}
            </button>

            <button
              onClick={handleDismiss}
              className="w-7 h-7 bg-white/20 hover:bg-red-400 rounded-full flex items-center justify-center transition-colors text-xs"
              title="Close"
            >
              ✕
            </button>
          </div>
        </div>

        {/* Animated Sound Waves */}
        {isPlaying && (
          <div className="absolute -bottom-1 left-1/2 transform -translate-x-1/2 flex space-x-0.5">
            <div className="w-0.5 h-2 bg-white/60 rounded-full music-wave" style={{ animationDelay: '0s' }}></div>
            <div className="w-0.5 h-3 bg-white/60 rounded-full music-wave" style={{ animationDelay: '0.1s' }}></div>
            <div className="w-0.5 h-2 bg-white/60 rounded-full music-wave" style={{ animationDelay: '0.2s' }}></div>
            <div className="w-0.5 h-4 bg-white/60 rounded-full music-wave" style={{ animationDelay: '0.3s' }}></div>
            <div className="w-0.5 h-2 bg-white/60 rounded-full music-wave" style={{ animationDelay: '0.4s' }}></div>
          </div>
        )}

        {/* Hidden YouTube Player */}
        <iframe
          ref={playerRef}
          width="0"
          height="0"
          src=""
          style={{ display: 'none' }}
          allow="autoplay; encrypted-media"
        />
      </div>
    </div>
  );
}
