"use client";

import { useState, useRef, useEffect } from 'react';
import { Home, User, Briefcase, Code, Mail, Play, Pause, Music, Volume2, VolumeX, ExternalLink } from 'lucide-react';

interface MobileBottomNavProps {
  scrollToSection: (sectionId: string) => void;
}

export default function MobileBottomNav({ scrollToSection }: MobileBottomNavProps) {
  // YouTube Player State
  const [isYouTubePlaying, setIsYouTubePlaying] = useState(false);
  const [isYouTubeMuted, setIsYouTubeMuted] = useState(false);
  const youtubePlayerRef = useRef<HTMLIFrameElement>(null);

  // Navigation State
  const [activeSection, setActiveSection] = useState('hero');

  // Spotify State
  const [isSpotifyExpanded, setIsSpotifyExpanded] = useState(false);

  const navItems = [
    { id: 'hero', icon: Home },
    { id: 'about', icon: User },
    { id: 'experience', icon: Briefcase },
    { id: 'contributions', icon: Code },
    { id: 'hire', icon: Mail },
  ];

  const handleNavClick = (sectionId: string) => {
    scrollToSection(sectionId);
    setActiveSection(sectionId);
  };

  const handleYouTubePlay = () => {
    if (youtubePlayerRef.current) {
      const iframe = youtubePlayerRef.current;
      if (isYouTubePlaying) {
        iframe.src = `https://www.youtube.com/embed/jfKfPfyJRdk?enablejsapi=1&controls=0&modestbranding=1&rel=0&showinfo=0`;
        setIsYouTubePlaying(false);
      } else {
        iframe.src = `https://www.youtube.com/embed/jfKfPfyJRdk?enablejsapi=1&autoplay=1&controls=0&modestbranding=1&rel=0&showinfo=0&mute=${isYouTubeMuted ? 1 : 0}`;
        setIsYouTubePlaying(true);
      }
    }
  };

  const handleYouTubeMute = () => {
    setIsYouTubeMuted(!isYouTubeMuted);
    if (youtubePlayerRef.current && isYouTubePlaying) {
      const iframe = youtubePlayerRef.current;
      iframe.src = `https://www.youtube.com/embed/jfKfPfyJRdk?enablejsapi=1&autoplay=1&controls=0&modestbranding=1&rel=0&showinfo=0&mute=${!isYouTubeMuted ? 1 : 0}`;
    }
  };

  const handleSpotifyClick = () => {
    window.open('https://open.spotify.com/playlist/37i9dQZF1DXcBWIGoYBM5M', '_blank');
  };

  return (
    <div className="fixed bottom-6 left-1/2 transform -translate-x-1/2 z-50 md:hidden">
      {/* Main Navigation Bar */}
      <div className="mobile-nav-glass rounded-full px-6 py-3 relative">
        <div className="flex items-center justify-between space-x-6">

          {/* YouTube Music Player */}
          <div className="flex items-center space-x-2">
            <button
              onClick={handleYouTubePlay}
              className={`
                w-12 h-12 rounded-full flex items-center justify-center transition-all duration-500 relative
                ${isYouTubePlaying
                  ? 'bg-red-500 text-white pulse-glow'
                  : 'bg-white/20 text-white hover:bg-white/30 hover:scale-110'
                }
              `}
            >
              {isYouTubePlaying ? (
                <div className="relative">
                  <Pause className="w-5 h-5" />
                  <div className="absolute -inset-1 rounded-full border-2 border-white/40 rotating-border"></div>
                </div>
              ) : (
                <Play className="w-5 h-5 ml-0.5" />
              )}
            </button>

            {isYouTubePlaying && (
              <button
                onClick={handleYouTubeMute}
                className="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center text-white hover:bg-white/30 transition-all duration-300 hover:scale-110"
              >
                {isYouTubeMuted ? <VolumeX className="w-3 h-3" /> : <Volume2 className="w-3 h-3" />}
              </button>
            )}
          </div>

          {/* Navigation Icons */}
          <div className="flex items-center space-x-2">
            {navItems.map((item) => {
              const IconComponent = item.icon;
              const isActive = activeSection === item.id;
              return (
                <button
                  key={item.id}
                  onClick={() => handleNavClick(item.id)}
                  className={`
                    w-11 h-11 rounded-full flex items-center justify-center transition-all duration-300 relative
                    ${isActive
                      ? 'bg-blue-500 text-white shadow-lg shadow-blue-500/40 scale-110'
                      : 'text-white/70 hover:text-white hover:bg-white/20 hover:scale-105'
                    }
                  `}
                >
                  <IconComponent className="w-4 h-4" />
                  {isActive && (
                    <div className="absolute -inset-0.5 rounded-full border border-blue-300/50 animate-ping"></div>
                  )}
                </button>
              );
            })}
          </div>

          {/* Spotify Music Player */}
          <div className="flex items-center space-x-2">
            <button
              onClick={handleSpotifyClick}
              className="w-12 h-12 bg-green-500 rounded-full flex items-center justify-center text-white hover:bg-green-600 transition-all duration-300 shadow-lg shadow-green-500/40 hover:scale-110 pulse-glow"
            >
              <Music className="w-5 h-5" />
            </button>
          </div>

        </div>

        {/* Animated Sound Waves for YouTube */}
        {isYouTubePlaying && (
          <div className="absolute -bottom-2 left-10 flex space-x-1">
            <div className="w-1 h-3 bg-red-400/80 rounded-full music-wave" style={{ animationDelay: '0s' }}></div>
            <div className="w-1 h-4 bg-red-400/80 rounded-full music-wave" style={{ animationDelay: '0.1s' }}></div>
            <div className="w-1 h-2 bg-red-400/80 rounded-full music-wave" style={{ animationDelay: '0.2s' }}></div>
            <div className="w-1 h-5 bg-red-400/80 rounded-full music-wave" style={{ animationDelay: '0.3s' }}></div>
            <div className="w-1 h-3 bg-red-400/80 rounded-full music-wave" style={{ animationDelay: '0.4s' }}></div>
          </div>
        )}

        {/* Spotify indicator */}
        <div className="absolute -bottom-2 right-10 flex space-x-1">
          <div className="w-1 h-2 bg-green-400/60 rounded-full animate-pulse" style={{ animationDelay: '0s' }}></div>
          <div className="w-1 h-3 bg-green-400/60 rounded-full animate-pulse" style={{ animationDelay: '0.2s' }}></div>
          <div className="w-1 h-2 bg-green-400/60 rounded-full animate-pulse" style={{ animationDelay: '0.4s' }}></div>
        </div>
      </div>

      {/* Hidden YouTube Player */}
      <iframe
        ref={youtubePlayerRef}
        width="0"
        height="0"
        src=""
        style={{ display: 'none' }}
        allow="autoplay; encrypted-media"
      />
    </div>
  );
}
