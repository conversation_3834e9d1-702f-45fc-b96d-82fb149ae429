"use client";

import { useState } from 'react';
import { Home, User, Briefcase, Code, Mail, Menu, X } from 'lucide-react';

interface MobileBottomNavProps {
  scrollToSection: (sectionId: string) => void;
}

export default function MobileBottomNav({ scrollToSection }: MobileBottomNavProps) {
  const [isExpanded, setIsExpanded] = useState(false);

  const navItems = [
    { id: 'hero', label: 'Home', icon: Home },
    { id: 'about', label: 'About', icon: User },
    { id: 'experience', label: 'Projects', icon: Briefcase },
    { id: 'contributions', label: 'Code', icon: Code },
    { id: 'hire', label: 'Hire Me', icon: Mail },
  ];

  const handleNavClick = (sectionId: string) => {
    scrollToSection(sectionId);
    setIsExpanded(false);
  };

  const toggleExpanded = () => {
    setIsExpanded(!isExpanded);
  };

  return (
    <div className="fixed bottom-4 left-1/2 transform -translate-x-1/2 z-50 md:hidden">
      {/* Expanded Navigation */}
      {isExpanded && (
        <div className="mb-4 bg-white/95 dark:bg-dark-surface/95 backdrop-blur-md rounded-2xl shadow-lg border border-deep-charcoal/10 dark:border-dark-text/10 p-3 animate-in slide-in-from-bottom-2 duration-300">
          <div className="grid grid-cols-3 gap-3">
            {navItems.map((item) => {
              const IconComponent = item.icon;
              return (
                <button
                  key={item.id}
                  onClick={() => handleNavClick(item.id)}
                  className="flex flex-col items-center space-y-1 p-3 rounded-xl hover:bg-deep-charcoal/10 dark:hover:bg-dark-text/10 transition-colors"
                >
                  <IconComponent className="w-5 h-5 text-deep-charcoal dark:text-dark-text" />
                  <span className="text-xs font-medium text-deep-charcoal dark:text-dark-text">
                    {item.label}
                  </span>
                </button>
              );
            })}
          </div>
        </div>
      )}

      {/* Toggle Button */}
      <button
        onClick={toggleExpanded}
        className={`
          w-14 h-14 bg-gradient-to-r from-deep-charcoal to-deep-charcoal/90 dark:from-dark-text dark:to-dark-text/90
          text-light-almond dark:text-dark-bg rounded-full shadow-lg hover:shadow-xl 
          transition-all duration-300 ease-out flex items-center justify-center
          ${isExpanded ? 'rotate-180' : ''}
        `}
      >
        {isExpanded ? (
          <X className="w-6 h-6" />
        ) : (
          <Menu className="w-6 h-6" />
        )}
      </button>

      {/* Background overlay when expanded */}
      {isExpanded && (
        <div 
          className="fixed inset-0 bg-black/20 backdrop-blur-sm -z-10"
          onClick={() => setIsExpanded(false)}
        />
      )}
    </div>
  );
}
